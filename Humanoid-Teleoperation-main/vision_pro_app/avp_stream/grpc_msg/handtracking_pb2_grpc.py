# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

import avp_stream.grpc_msg.handtracking_pb2 as handtracking__pb2


class HandTrackingServiceStub(object):
    """The hand tracking service definition.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StreamHandUpdates = channel.unary_stream(
                '/handtracking.HandTrackingService/StreamHandUpdates',
                request_serializer=handtracking__pb2.HandUpdate.SerializeToString,
                response_deserializer=handtracking__pb2.HandUpdate.FromString,
                )


class HandTrackingServiceServicer(object):
    """The hand tracking service definition.
    """

    def StreamHandUpdates(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_HandTrackingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StreamHandUpdates': grpc.unary_stream_rpc_method_handler(
                    servicer.StreamHandUpdates,
                    request_deserializer=handtracking__pb2.HandUpdate.FromString,
                    response_serializer=handtracking__pb2.HandUpdate.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'handtracking.HandTrackingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class HandTrackingService(object):
    """The hand tracking service definition.
    """

    @staticmethod
    def StreamHandUpdates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/handtracking.HandTrackingService/StreamHandUpdates',
            handtracking__pb2.HandUpdate.SerializeToString,
            handtracking__pb2.HandUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
