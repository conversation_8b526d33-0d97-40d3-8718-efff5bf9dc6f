//
// DO NOT EDIT.
// swift-format-ignore-file
//
// Generated by the protocol buffer compiler.
// Source: handtracking.proto
//
import GRPC
import NIO
import NIOConcurrencyHelpers
import SwiftProtobuf


/// The hand tracking service definition.
///
/// Usage: instantiate `Handtracking_HandTrackingServiceClient`, then call methods of this protocol to make API calls.
internal protocol Handtracking_HandTrackingServiceClientProtocol: GRPCClient {
  var serviceName: String { get }
  var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? { get }

  func streamHandUpdates(
    _ request: Handtracking_HandUpdate,
    callOptions: CallOptions?,
    handler: @escaping (Handtracking_HandUpdate) -> Void
  ) -> ServerStreamingCall<Handtracking_HandUpdate, Handtracking_HandUpdate>
}

extension Handtracking_HandTrackingServiceClientProtocol {
  internal var serviceName: String {
    return "handtracking.HandTrackingService"
  }

  /// Server streaming call to StreamHandUpdates
  ///
  /// - Parameters:
  ///   - request: Request to send to StreamHandUpdates.
  ///   - callOptions: Call options.
  ///   - handler: A closure called when each response is received from the server.
  /// - Returns: A `ServerStreamingCall` with futures for the metadata and status.
  internal func streamHandUpdates(
    _ request: Handtracking_HandUpdate,
    callOptions: CallOptions? = nil,
    handler: @escaping (Handtracking_HandUpdate) -> Void
  ) -> ServerStreamingCall<Handtracking_HandUpdate, Handtracking_HandUpdate> {
    return self.makeServerStreamingCall(
      path: Handtracking_HandTrackingServiceClientMetadata.Methods.streamHandUpdates.path,
      request: request,
      callOptions: callOptions ?? self.defaultCallOptions,
      interceptors: self.interceptors?.makeStreamHandUpdatesInterceptors() ?? [],
      handler: handler
    )
  }
}

@available(*, deprecated)
extension Handtracking_HandTrackingServiceClient: @unchecked Sendable {}

@available(*, deprecated, renamed: "Handtracking_HandTrackingServiceNIOClient")
internal final class Handtracking_HandTrackingServiceClient: Handtracking_HandTrackingServiceClientProtocol {
  private let lock = Lock()
  private var _defaultCallOptions: CallOptions
  private var _interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol?
  internal let channel: GRPCChannel
  internal var defaultCallOptions: CallOptions {
    get { self.lock.withLock { return self._defaultCallOptions } }
    set { self.lock.withLockVoid { self._defaultCallOptions = newValue } }
  }
  internal var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? {
    get { self.lock.withLock { return self._interceptors } }
    set { self.lock.withLockVoid { self._interceptors = newValue } }
  }

  /// Creates a client for the handtracking.HandTrackingService service.
  ///
  /// - Parameters:
  ///   - channel: `GRPCChannel` to the service host.
  ///   - defaultCallOptions: Options to use for each service call if the user doesn't provide them.
  ///   - interceptors: A factory providing interceptors for each RPC.
  internal init(
    channel: GRPCChannel,
    defaultCallOptions: CallOptions = CallOptions(),
    interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? = nil
  ) {
    self.channel = channel
    self._defaultCallOptions = defaultCallOptions
    self._interceptors = interceptors
  }
}

internal struct Handtracking_HandTrackingServiceNIOClient: Handtracking_HandTrackingServiceClientProtocol {
  internal var channel: GRPCChannel
  internal var defaultCallOptions: CallOptions
  internal var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol?

  /// Creates a client for the handtracking.HandTrackingService service.
  ///
  /// - Parameters:
  ///   - channel: `GRPCChannel` to the service host.
  ///   - defaultCallOptions: Options to use for each service call if the user doesn't provide them.
  ///   - interceptors: A factory providing interceptors for each RPC.
  internal init(
    channel: GRPCChannel,
    defaultCallOptions: CallOptions = CallOptions(),
    interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? = nil
  ) {
    self.channel = channel
    self.defaultCallOptions = defaultCallOptions
    self.interceptors = interceptors
  }
}

/// The hand tracking service definition.
@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
internal protocol Handtracking_HandTrackingServiceAsyncClientProtocol: GRPCClient {
  static var serviceDescriptor: GRPCServiceDescriptor { get }
  var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? { get }

  func makeStreamHandUpdatesCall(
    _ request: Handtracking_HandUpdate,
    callOptions: CallOptions?
  ) -> GRPCAsyncServerStreamingCall<Handtracking_HandUpdate, Handtracking_HandUpdate>
}

@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
extension Handtracking_HandTrackingServiceAsyncClientProtocol {
  internal static var serviceDescriptor: GRPCServiceDescriptor {
    return Handtracking_HandTrackingServiceClientMetadata.serviceDescriptor
  }

  internal var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? {
    return nil
  }

  internal func makeStreamHandUpdatesCall(
    _ request: Handtracking_HandUpdate,
    callOptions: CallOptions? = nil
  ) -> GRPCAsyncServerStreamingCall<Handtracking_HandUpdate, Handtracking_HandUpdate> {
    return self.makeAsyncServerStreamingCall(
      path: Handtracking_HandTrackingServiceClientMetadata.Methods.streamHandUpdates.path,
      request: request,
      callOptions: callOptions ?? self.defaultCallOptions,
      interceptors: self.interceptors?.makeStreamHandUpdatesInterceptors() ?? []
    )
  }
}

@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
extension Handtracking_HandTrackingServiceAsyncClientProtocol {
  internal func streamHandUpdates(
    _ request: Handtracking_HandUpdate,
    callOptions: CallOptions? = nil
  ) -> GRPCAsyncResponseStream<Handtracking_HandUpdate> {
    return self.performAsyncServerStreamingCall(
      path: Handtracking_HandTrackingServiceClientMetadata.Methods.streamHandUpdates.path,
      request: request,
      callOptions: callOptions ?? self.defaultCallOptions,
      interceptors: self.interceptors?.makeStreamHandUpdatesInterceptors() ?? []
    )
  }
}

@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
internal struct Handtracking_HandTrackingServiceAsyncClient: Handtracking_HandTrackingServiceAsyncClientProtocol {
  internal var channel: GRPCChannel
  internal var defaultCallOptions: CallOptions
  internal var interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol?

  internal init(
    channel: GRPCChannel,
    defaultCallOptions: CallOptions = CallOptions(),
    interceptors: Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol? = nil
  ) {
    self.channel = channel
    self.defaultCallOptions = defaultCallOptions
    self.interceptors = interceptors
  }
}

internal protocol Handtracking_HandTrackingServiceClientInterceptorFactoryProtocol: Sendable {

  /// - Returns: Interceptors to use when invoking 'streamHandUpdates'.
  func makeStreamHandUpdatesInterceptors() -> [ClientInterceptor<Handtracking_HandUpdate, Handtracking_HandUpdate>]
}

internal enum Handtracking_HandTrackingServiceClientMetadata {
  internal static let serviceDescriptor = GRPCServiceDescriptor(
    name: "HandTrackingService",
    fullName: "handtracking.HandTrackingService",
    methods: [
      Handtracking_HandTrackingServiceClientMetadata.Methods.streamHandUpdates,
    ]
  )

  internal enum Methods {
    internal static let streamHandUpdates = GRPCMethodDescriptor(
      name: "StreamHandUpdates",
      path: "/handtracking.HandTrackingService/StreamHandUpdates",
      type: GRPCCallType.serverStreaming
    )
  }
}

/// The hand tracking service definition.
///
/// To build a server, implement a class that conforms to this protocol.
internal protocol Handtracking_HandTrackingServiceProvider: CallHandlerProvider {
  var interceptors: Handtracking_HandTrackingServiceServerInterceptorFactoryProtocol? { get }

  func streamHandUpdates(request: Handtracking_HandUpdate, context: StreamingResponseCallContext<Handtracking_HandUpdate>) -> EventLoopFuture<GRPCStatus>
}

extension Handtracking_HandTrackingServiceProvider {
  internal var serviceName: Substring {
    return Handtracking_HandTrackingServiceServerMetadata.serviceDescriptor.fullName[...]
  }

  /// Determines, calls and returns the appropriate request handler, depending on the request's method.
  /// Returns nil for methods not handled by this service.
  internal func handle(
    method name: Substring,
    context: CallHandlerContext
  ) -> GRPCServerHandlerProtocol? {
    switch name {
    case "StreamHandUpdates":
      return ServerStreamingServerHandler(
        context: context,
        requestDeserializer: ProtobufDeserializer<Handtracking_HandUpdate>(),
        responseSerializer: ProtobufSerializer<Handtracking_HandUpdate>(),
        interceptors: self.interceptors?.makeStreamHandUpdatesInterceptors() ?? [],
        userFunction: self.streamHandUpdates(request:context:)
      )

    default:
      return nil
    }
  }
}

/// The hand tracking service definition.
///
/// To implement a server, implement an object which conforms to this protocol.
@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
internal protocol Handtracking_HandTrackingServiceAsyncProvider: CallHandlerProvider, Sendable {
  static var serviceDescriptor: GRPCServiceDescriptor { get }
  var interceptors: Handtracking_HandTrackingServiceServerInterceptorFactoryProtocol? { get }

  func streamHandUpdates(
    request: Handtracking_HandUpdate,
    responseStream: GRPCAsyncResponseStreamWriter<Handtracking_HandUpdate>,
    context: GRPCAsyncServerCallContext
  ) async throws
}

@available(macOS 10.15, iOS 13, tvOS 13, watchOS 6, *)
extension Handtracking_HandTrackingServiceAsyncProvider {
  internal static var serviceDescriptor: GRPCServiceDescriptor {
    return Handtracking_HandTrackingServiceServerMetadata.serviceDescriptor
  }

  internal var serviceName: Substring {
    return Handtracking_HandTrackingServiceServerMetadata.serviceDescriptor.fullName[...]
  }

  internal var interceptors: Handtracking_HandTrackingServiceServerInterceptorFactoryProtocol? {
    return nil
  }

  internal func handle(
    method name: Substring,
    context: CallHandlerContext
  ) -> GRPCServerHandlerProtocol? {
    switch name {
    case "StreamHandUpdates":
      return GRPCAsyncServerHandler(
        context: context,
        requestDeserializer: ProtobufDeserializer<Handtracking_HandUpdate>(),
        responseSerializer: ProtobufSerializer<Handtracking_HandUpdate>(),
        interceptors: self.interceptors?.makeStreamHandUpdatesInterceptors() ?? [],
        wrapping: { try await self.streamHandUpdates(request: $0, responseStream: $1, context: $2) }
      )

    default:
      return nil
    }
  }
}

internal protocol Handtracking_HandTrackingServiceServerInterceptorFactoryProtocol: Sendable {

  /// - Returns: Interceptors to use when handling 'streamHandUpdates'.
  ///   Defaults to calling `self.makeInterceptors()`.
  func makeStreamHandUpdatesInterceptors() -> [ServerInterceptor<Handtracking_HandUpdate, Handtracking_HandUpdate>]
}

internal enum Handtracking_HandTrackingServiceServerMetadata {
  internal static let serviceDescriptor = GRPCServiceDescriptor(
    name: "HandTrackingService",
    fullName: "handtracking.HandTrackingService",
    methods: [
      Handtracking_HandTrackingServiceServerMetadata.Methods.streamHandUpdates,
    ]
  )

  internal enum Methods {
    internal static let streamHandUpdates = GRPCMethodDescriptor(
      name: "StreamHandUpdates",
      path: "/handtracking.HandTrackingService/StreamHandUpdates",
      type: GRPCCallType.serverStreaming
    )
  }
}
