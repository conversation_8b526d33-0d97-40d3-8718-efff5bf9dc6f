{"sourceLanguage": "en", "strings": {"About": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "translated", "value": "アプリについて"}}}}, "Exit": {"localizations": {"ja": {"stringUnit": {"state": "translated", "value": "終了"}}}}, "Fix / Unfix a pointer by indirect tap.": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "translated", "value": "間接タップでポインターを固定できます。"}}}}, "Hand tracking authorization:": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "translated", "value": "ハンドトラッキング許可状況:"}}}}, "HandsWidth": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "needs_review", "value": "手がメジャー"}}}}, "Measurement of the distance between the fingers.": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "translated", "value": "指と指の間の距離を測ります。"}}}}, "Setting": {"extractionState": "stale", "localizations": {"ja": {"stringUnit": {"state": "translated", "value": "設定"}}}}, "Start": {"localizations": {"ja": {"stringUnit": {"state": "translated", "value": "開始"}}}}, "Unit": {"localizations": {"ja": {"stringUnit": {"state": "translated", "value": "単位"}}}}, "You're on IP address [%@]": {}}, "version": "1.0"}